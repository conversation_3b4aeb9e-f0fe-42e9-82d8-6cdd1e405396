/**
 * Modern Login Screen with Passwordless Authentication
 * 
 * Enhanced login screen that implements REC-FORM-001 requirements:
 * - Passwordless authentication as primary method
 * - Traditional password login as fallback
 * - Proactive form validation with real-time feedback
 * - Comprehensive accessibility support
 * - Modern UI with smooth animations
 * 
 * Features:
 * - Email magic links and SMS OTP
 * - Biometric authentication support
 * - Progressive enhancement approach
 * - WCAG 2.2 AA compliant
 * - Dark mode support
 * - Comprehensive error handling
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useNavigation } from '@react-navigation/native';
import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  AccessibilityInfo,
} from 'react-native';

import { Box } from '../../components/atoms/Box';
import { Text } from '../../components/atoms/Text';
import { Button } from '../../components/atoms/Button';
import { Input } from '../../components/atoms/Input';
import { PasswordlessAuthForm, PasswordlessFormData } from '../../components/forms/PasswordlessAuthForm';
import { useTheme } from '../../contexts/ThemeContext';
import { authService } from '../../services/authService';
import { useAuthStore } from '../../store/authSlice';
import { Colors } from '../../constants/Colors';

// Authentication mode types
type AuthMode = 'passwordless' | 'traditional';

// Traditional form data
interface TraditionalFormData {
  email: string;
  password: string;
}

// Form validation errors
interface FormErrors {
  email?: string;
  password?: string;
}

export const ModernLoginScreen: React.FC = () => {
  const navigation = useNavigation();
  const { isDark } = useTheme();
  const isDarkMode = isDark;
  const { status, error, loginStart, loginSuccess, loginFailure } = useSafeAuthStore();

  // Authentication mode state
  const [authMode, setAuthMode] = useState<AuthMode>('passwordless');
  
  // Traditional form state
  const [traditionalForm, setTraditionalForm] = useState<TraditionalFormData>({
    email: '',
    password: '',
  });
  const [traditionalErrors, setTraditionalErrors] = useState<FormErrors>({});
  const [hasInteracted, setHasInteracted] = useState({
    email: false,
    password: false,
  });

  const isLoading = status === 'loading';

  // Handle passwordless authentication success
  const handlePasswordlessSuccess = useCallback(async (data: PasswordlessFormData & { user?: any; token?: string }) => {
    try {
      loginStart();

      // If token is already provided by the passwordless service, use it directly
      if (data.user && data.token) {
        // Create user object with proper structure
        const user = {
          id: data.user.id || 'temp_id',
          email: data.user.email || data.email || '',
          firstName: data.user.firstName || data.user.first_name || '',
          lastName: data.user.lastName || data.user.last_name || '',
          role: data.user.role === 'service_provider' ? 'provider' as const : 'customer' as const,
          isVerified: data.user.isVerified || false,
          createdAt: data.user.createdAt || new Date().toISOString(),
          updatedAt: data.user.updatedAt || new Date().toISOString(),
        };

        loginSuccess(data.token, data.token, user); // Using token as both auth and refresh token

        AccessibilityInfo.announceForAccessibility('Login successful');
        return;
      }

      // Otherwise, use the auth service
      const response = await authService.passwordlessLogin({
        method: data.selectedMethod,
        email: data.email,
        phone: data.phone,
        verificationCode: data.verificationCode,
      });

      // Create user object with proper structure
      const user = {
        id: response.user.id,
        email: response.user.email,
        firstName: response.user.firstName || response.user.first_name || '',
        lastName: response.user.lastName || response.user.last_name || '',
        role: response.user.role === 'service_provider' ? 'provider' as const : 'customer' as const,
        isVerified: response.user.isVerified || false,
        createdAt: response.user.createdAt || new Date().toISOString(),
        updatedAt: response.user.updatedAt || new Date().toISOString(),
      };

      loginSuccess(response.access, response.refresh || response.access, user);

      AccessibilityInfo.announceForAccessibility('Login successful');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Passwordless login failed';
      loginFailure(errorMessage);
    }
  }, [loginStart, loginSuccess, loginFailure]);

  // Handle passwordless authentication error
  const handlePasswordlessError = useCallback((error: string) => {
    loginFailure(error);
    AccessibilityInfo.announceForAccessibility(`Login error: ${error}`);
  }, [loginFailure]);

  // Handle switch to traditional authentication
  const handleTraditionalFallback = useCallback(() => {
    setAuthMode('traditional');
    AccessibilityInfo.announceForAccessibility('Switched to traditional password login');
  }, []);

  // Handle traditional form input changes
  const handleTraditionalInputChange = useCallback((field: keyof TraditionalFormData, value: string) => {
    setTraditionalForm(prev => ({ ...prev, [field]: value }));
    setHasInteracted(prev => ({ ...prev, [field]: true }));
    
    // Clear field-specific errors
    if (traditionalErrors[field]) {
      setTraditionalErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [traditionalErrors]);

  // Validate traditional form
  const validateTraditionalForm = useCallback((): boolean => {
    const errors: FormErrors = {};

    if (!traditionalForm.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(traditionalForm.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!traditionalForm.password.trim()) {
      errors.password = 'Password is required';
    } else if (traditionalForm.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    setTraditionalErrors(errors);
    return Object.keys(errors).length === 0;
  }, [traditionalForm]);

  // Handle traditional login
  const handleTraditionalLogin = useCallback(async () => {
    // Mark all fields as interacted for validation display
    setHasInteracted({
      email: true,
      password: true,
    });

    if (!validateTraditionalForm()) {
      return;
    }

    try {
      loginStart();

      const response = await authService.login({
        email: traditionalForm.email,
        password: traditionalForm.password,
      });

      // Create user object with proper structure
      const user = {
        id: response.user.id,
        email: response.user.email,
        firstName: response.user.firstName || response.user.first_name || '',
        lastName: response.user.lastName || response.user.last_name || '',
        role: response.user.role === 'service_provider' ? 'provider' as const : 'customer' as const,
        isVerified: response.user.isVerified || false,
        createdAt: response.user.createdAt || new Date().toISOString(),
        updatedAt: response.user.updatedAt || new Date().toISOString(),
      };

      loginSuccess(response.access, response.refresh || response.access, user);

      AccessibilityInfo.announceForAccessibility('Login successful');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      loginFailure(errorMessage);
    }
  }, [traditionalForm, validateTraditionalForm, loginStart, loginSuccess, loginFailure]);

  // Handle navigation to registration
  const handleRegisterNavigation = useCallback(() => {
    navigation.navigate('Register' as never);
  }, [navigation]);

  // Handle switch back to passwordless
  const handleSwitchToPasswordless = useCallback(() => {
    setAuthMode('passwordless');
    AccessibilityInfo.announceForAccessibility('Switched to passwordless authentication');
  }, []);

  // Render passwordless authentication
  const renderPasswordlessAuth = () => (
    <PasswordlessAuthForm
      onSuccess={handlePasswordlessSuccess}
      onError={handlePasswordlessError}
      onTraditionalFallback={handleTraditionalFallback}
      enabledMethods={['email', 'phone', 'biometric']}
      defaultMethod="email"
      showTraditionalFallback={true}
      testID="passwordless-auth"
    />
  );

  // Render traditional authentication
  const renderTraditionalAuth = () => (
    <Box style={styles.traditionalContainer}>
      <Text
        variant="heading"
        size="2xl"
        weight="bold"
        color={isDarkMode ? Colors.textSecondary : Colors.textPrimary}
        style={styles.title}
      >
        Welcome Back
      </Text>
      
      <Text
        variant="body"
        color={isDarkMode ? Colors.textSecondary : Colors.textSecondary}
        style={styles.subtitle}
      >
        Sign in with your email and password
      </Text>

      <View style={styles.form}>
        <Input
          placeholder="Enter your email"
          value={traditionalForm.email}
          onChangeText={(value) => handleTraditionalInputChange('email', value)}
          keyboardType="email-address"
          autoCapitalize="none"
          error={hasInteracted.email ? traditionalErrors.email : undefined}
          editable={!isLoading}
          testID="traditional-email-input"
        />

        <Input
          placeholder="Enter your password"
          value={traditionalForm.password}
          onChangeText={(value) => handleTraditionalInputChange('password', value)}
          secureTextEntry
          error={hasInteracted.password ? traditionalErrors.password : undefined}
          editable={!isLoading}
          testID="traditional-password-input"
        />

        {/* Error Display */}
        {error && (
          <Text
            variant="body"
            color={Colors.error}
            style={styles.errorText}
          >
            {error}
          </Text>
        )}

        {/* Login Button */}
        <Button
          onPress={handleTraditionalLogin}
          variant="primary"
          style={styles.loginButton}
          disabled={isLoading}
          testID="traditional-login-button"
        >
          <Text variant="button" color={Colors.textInverse}>
            {isLoading ? 'Signing In...' : 'Sign In'}
          </Text>
        </Button>

        {/* Switch to Passwordless */}
        <Button
          onPress={handleSwitchToPasswordless}
          variant="outline"
          style={styles.switchButton}
          disabled={isLoading}
          testID="switch-to-passwordless"
        >
          <Text variant="button" color={Colors.textSecondary}>
            Try Passwordless Login
          </Text>
        </Button>

        {/* Register Link */}
        <TouchableOpacity
          onPress={handleRegisterNavigation}
          disabled={isLoading}
          style={styles.registerLink}
          testID="register-link"
        >
          <Text
            variant="body"
            color={isDarkMode ? Colors.textSecondary : Colors.textSecondary}
            style={styles.registerLinkText}
          >
            Don't have an account?{' '}
            <Text
              variant="body"
              color={Colors.primary.sage}
              style={styles.registerLinkBold}
            >
              Sign Up
            </Text>
          </Text>
        </TouchableOpacity>
      </View>
    </Box>
  );

  return (
    <View
      style={[
        styles.safeArea,
        { backgroundColor: isDarkMode ? Colors.backgroundSecondary : Colors.backgroundPrimary }
      ]}
      testID="modern-login-screen"
    >
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {authMode === 'passwordless' ? renderPasswordlessAuth() : renderTraditionalAuth()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  traditionalContainer: {
    flex: 1,
    justifyContent: 'center',
    maxWidth: 400,
    alignSelf: 'center',
    width: '100%',
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 32,
  },
  form: {
    gap: 16,
  },
  errorText: {
    textAlign: 'center',
    marginVertical: 8,
  },
  loginButton: {
    marginTop: 8,
  },
  switchButton: {
    marginTop: 16,
  },
  registerLink: {
    alignItems: 'center',
    marginTop: 24,
    padding: 8,
  },
  registerLinkText: {
    textAlign: 'center',
  },
  registerLinkBold: {
    fontWeight: '600',
  },
});

export default ModernLoginScreen;
