/**
 * FormExample Component - Comprehensive Form Components Demo
 *
 * Component Contract:
 * - Demonstrates all form components in action
 * - Shows validation patterns and error handling
 * - Provides reference implementation for developers
 * - Tests form component integration
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet, Alert } from 'react-native';
import { constructiveAlert } from '../../utils/constructiveErrorHandler';

import { Colors } from '../../constants/Colors';
import {
  getResponsiveSpacing,
  getResponsiveFontSize,
} from '../../utils/responsiveUtils';
import { Button } from '../atoms/Button';

import {
  FormInput,
  FormSelect,
  FormCheckbox,
  FormCheckboxGroup,
  FormRadio,
  FormRadioGroup,
  FormValidator,
  CommonValidations,
} from './index';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  serviceCategory: string;
  preferredTime: string;
  notifications: boolean;
  marketingEmails: boolean;
  interests: (string | number)[];
  experience: string;
  additionalInfo: string;
}

const initialFormData: FormData = {
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  serviceCategory: '',
  preferredTime: '',
  notifications: false,
  marketingEmails: false,
  interests: [],
  experience: '',
  additionalInfo: '',
};

export const FormExample: React.FC = () => {
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form validator instance
  const formValidator = new FormValidator({
    firstName: CommonValidations.name,
    lastName: CommonValidations.name,
    email: CommonValidations.email,
    phone: CommonValidations.phone,
    password: CommonValidations.password,
    confirmPassword: CommonValidations.confirmPassword('password'),
    serviceCategory: CommonValidations.required,
    preferredTime: CommonValidations.required,
    experience: CommonValidations.required,
  });

  // Service category options
  const serviceCategoryOptions = [
    { label: 'Hair Services', value: 'hair', icon: 'hairServices' },
    { label: 'Nail Services', value: 'nails', icon: 'nailServices' },
    { label: 'Lash Services', value: 'lashes', icon: 'lashServices' },
    { label: 'Braiding', value: 'braiding', icon: 'braiding' },
    { label: 'Skincare', value: 'skincare', icon: 'skincare' },
    { label: 'Massage', value: 'massage', icon: 'massage' },
  ];

  // Interest options for checkbox group
  const interestOptions = [
    { label: 'Special Offers', value: 'offers' },
    { label: 'New Services', value: 'services' },
    { label: 'Beauty Tips', value: 'tips' },
    { label: 'Events & Workshops', value: 'events' },
  ];

  // Experience options for radio group
  const experienceOptions = [
    {
      label: 'Beginner',
      value: 'beginner',
      description: 'New to beauty services',
    },
    {
      label: 'Intermediate',
      value: 'intermediate',
      description: 'Some experience with beauty services',
    },
    {
      label: 'Advanced',
      value: 'advanced',
      description: 'Regular beauty service user',
    },
    {
      label: 'Professional',
      value: 'professional',
      description: 'Work in the beauty industry',
    },
  ];

  // Time preference options
  const timeOptions = [
    { label: 'Morning (8AM - 12PM)', value: 'morning' },
    { label: 'Afternoon (12PM - 5PM)', value: 'afternoon' },
    { label: 'Evening (5PM - 8PM)', value: 'evening' },
    { label: 'Weekend Only', value: 'weekend' },
    { label: 'Flexible', value: 'flexible' },
  ];

  const updateFormData = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    formValidator.setFieldTouched(field, true);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    // Validate entire form
    const validationResult = formValidator.validateForm(formData);

    if (!validationResult.isValid) {
      Alert.alert(
        'Validation Error',
        'Please correct the errors in the form before submitting.',
      );
      setIsSubmitting(false);
      return;
    }

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      Alert.alert('Success!', 'Form submitted successfully!', [
        {
          text: 'OK',
          onPress: () => {
            setFormData(initialFormData);
            formValidator.reset();
          },
        },
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to submit form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Form Components Demo</Text>
        <Text style={styles.subtitle}>
          Complete form with validation, error handling, and accessibility
        </Text>
      </View>

      <View style={styles.form}>
        {/* Personal Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Personal Information</Text>

          <FormInput
            label="First Name"
            placeholder="Enter your first name"
            value={formData.firstName}
            onChangeText={value => updateFormData('firstName', value)}
            inputType="name"
            required
            error={formValidator.getFieldError('firstName')}
            onValidation={(isValid, error) => {
              if (!isValid && error) {
                formValidator.validateField(
                  'firstName',
                  formData.firstName,
                  formData,
                );
              }
            }}
          />

          <FormInput
            label="Last Name"
            placeholder="Enter your last name"
            value={formData.lastName}
            onChangeText={value => updateFormData('lastName', value)}
            inputType="name"
            required
            error={formValidator.getFieldError('lastName')}
            onValidation={(isValid, error) => {
              if (!isValid && error) {
                formValidator.validateField(
                  'lastName',
                  formData.lastName,
                  formData,
                );
              }
            }}
          />

          <FormInput
            label="Email Address"
            placeholder="Enter your email"
            value={formData.email}
            onChangeText={value => updateFormData('email', value)}
            inputType="email"
            required
            error={formValidator.getFieldError('email')}
            onValidation={(isValid, error) => {
              if (!isValid && error) {
                formValidator.validateField('email', formData.email, formData);
              }
            }}
          />

          <FormInput
            label="Phone Number"
            placeholder="Enter your phone number"
            value={formData.phone}
            onChangeText={value => updateFormData('phone', value)}
            inputType="phone"
            required
            error={formValidator.getFieldError('phone')}
            onValidation={(isValid, error) => {
              if (!isValid && error) {
                formValidator.validateField('phone', formData.phone, formData);
              }
            }}
          />
        </View>

        {/* Security Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Security</Text>

          <FormInput
            label="Password"
            placeholder="Create a password"
            value={formData.password}
            onChangeText={value => updateFormData('password', value)}
            inputType="password"
            required
            helperText="Must be at least 8 characters with uppercase, lowercase, and number"
            error={formValidator.getFieldError('password')}
            onValidation={(isValid, error) => {
              if (!isValid && error) {
                formValidator.validateField(
                  'password',
                  formData.password,
                  formData,
                );
              }
            }}
          />

          <FormInput
            label="Confirm Password"
            placeholder="Confirm your password"
            value={formData.confirmPassword}
            onChangeText={value => updateFormData('confirmPassword', value)}
            inputType="password"
            required
            error={formValidator.getFieldError('confirmPassword')}
            onValidation={(isValid, error) => {
              if (!isValid && error) {
                formValidator.validateField(
                  'confirmPassword',
                  formData.confirmPassword,
                  formData,
                );
              }
            }}
          />
        </View>

        {/* Preferences Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Service Preferences</Text>

          <FormSelect
            label="Primary Service Category"
            placeholder="Select your main interest"
            options={serviceCategoryOptions}
            value={formData.serviceCategory}
            onSelectionChange={value =>
              updateFormData('serviceCategory', value)
            }
            searchable
            required
            error={formValidator.getFieldError('serviceCategory')}
            onValidation={(isValid, error) => {
              if (!isValid && error) {
                formValidator.validateField(
                  'serviceCategory',
                  formData.serviceCategory,
                  formData,
                );
              }
            }}
          />

          <FormSelect
            label="Preferred Appointment Time"
            placeholder="Select your preferred time"
            options={timeOptions}
            value={formData.preferredTime}
            onSelectionChange={value => updateFormData('preferredTime', value)}
            required
            error={formValidator.getFieldError('preferredTime')}
            onValidation={(isValid, error) => {
              if (!isValid && error) {
                formValidator.validateField(
                  'preferredTime',
                  formData.preferredTime,
                  formData,
                );
              }
            }}
          />

          <FormRadioGroup
            label="Experience Level"
            options={experienceOptions}
            selectedValue={formData.experience}
            onSelectionChange={value => updateFormData('experience', value)}
            required
            error={formValidator.getFieldError('experience')}
            onValidation={(isValid, error) => {
              if (!isValid && error) {
                formValidator.validateField(
                  'experience',
                  formData.experience,
                  formData,
                );
              }
            }}
          />
        </View>

        {/* Communication Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Communication Preferences</Text>

          <FormCheckbox
            label="Enable push notifications"
            checked={formData.notifications}
            onPress={checked => updateFormData('notifications', checked)}
            helperText="Receive appointment reminders and updates"
          />

          <FormCheckbox
            label="Subscribe to marketing emails"
            checked={formData.marketingEmails}
            onPress={checked => updateFormData('marketingEmails', checked)}
            helperText="Get special offers and beauty tips"
          />

          <FormCheckboxGroup
            label="Interests (Select all that apply)"
            options={interestOptions}
            selectedValues={formData.interests}
            onSelectionChange={values => updateFormData('interests', values)}
            helperText="Help us personalize your experience"
          />
        </View>

        {/* Additional Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Additional Information</Text>

          <FormInput
            label="Additional Comments"
            placeholder="Tell us anything else we should know..."
            value={formData.additionalInfo}
            onChangeText={value => updateFormData('additionalInfo', value)}
            multiline
            numberOfLines={4}
            helperText="Optional - any special requests or information"
          />
        </View>

        {/* Submit Button */}
        <View style={styles.submitSection}>
          <Button
            title={isSubmitting ? 'Submitting...' : 'Submit Form'}
            onPress={handleSubmit}
            disabled={isSubmitting}
            style={styles.submitButton}
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    padding: getResponsiveSpacing(24),
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  title: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: '700',
    color: Colors.text.primary,
    marginBottom: getResponsiveSpacing(8),
  },
  subtitle: {
    fontSize: getResponsiveFontSize(16),
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  form: {
    padding: getResponsiveSpacing(24),
  },
  section: {
    marginBottom: getResponsiveSpacing(32),
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: getResponsiveSpacing(16),
    paddingBottom: getResponsiveSpacing(8),
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  submitSection: {
    marginTop: getResponsiveSpacing(24),
    paddingTop: getResponsiveSpacing(24),
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },
  submitButton: {
    marginBottom: getResponsiveSpacing(24),
  },
});

export default FormExample;
